package publicModels

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/structure"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

type AccountPublic struct {
	ID        uuid.UUID `gorm:"column:id;type:uuid;primary_key;not null;default:uuid_generate_v4();"`
	Firstname string    `gorm:"column:firstname;type:character varying(64)"`
	Lastname  string    `gorm:"column:lastname;type:character varying(64)"`
}

func (AccountPublic) TableName() string {
	return "account"
}

type ContentForHandlebars struct {
	ID               uuid.UUID                `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Type             commonModels.ContentType `json:"type" gorm:"column:type;type:content_type"`
	Owner            uuid.UUID                `json:"owner" gorm:"column:owner;type:uuid" `
	Publisher        uuid.UUID                `json:"publisher" gorm:"column:publisher;type:uuid"`
	Title            string                   `json:"title" gorm:"column:title;type:character varying(256)"`
	Content          string                   `json:"content" gorm:"column:content;type:text"`
	Data             json.RawMessage          `json:"data" gorm:"column:data;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
	Structure        json.RawMessage          `json:"structure" gorm:"column:structure;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
	Route            string                   `json:"route" gorm:"column:route;type:character varying(256)"`
	Path             string                   `json:"path" gorm:"column:path;type:ltree"`
	PageLayout       commonModels.PageType    `json:"pagelayout" gorm:"column:pagelayout;type:page_style;DEFAULT:'HTML'::page_style;NOT NULL"`
	Created          time.Time                `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
	Updated          time.Time                `json:"updated" gorm:"column:updated;type:timestamp with time zone"`
	Deleted          time.Time                `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
	PrivacyLevel     int                      `json:"privacyLevel" gorm:"column:privacy_level;type:integer"`
	Approved         bool                     `json:"approved" gorm:"column:approved;type:boolean;DEFAULT:true"`
	Active           bool                     `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
	Sites            dbDriver.PgUUIDArray     `json:"sites" gorm:"column:sites;type:uuid[]"`
	SiteStructs      []tenancyModels.Site     `json:"sitesStructs" gorm:"-"`
	MediaID          uuid.NullUUID            `json:"mediaId" gorm:"column:media_id;type:uuid"`
	Settings         json.RawMessage          `json:"settings" gorm:"column:settings;type:jsonb;NOT NULL;DEFAULT '{}'::jsonb"`
	StructureID      *uuid.UUID               `json:"structureId" gorm:"column:structure_id;type:uuid;"`
	ContentStructure *structure.Structure     `json:"contentStructure" gorm:"foreignKey:structure_id"`
	Status           string                   `json:"status" gorm:"-"`
	Tags             []commonModels.Tag       `json:"tags" gorm:"-"`
	TagIds           dbDriver.PgUUIDArray     `gorm:"column:tags;type:uuid[]"`
	Media            commonModels.Media       `json:"media" gorm:"foreignKey:media_id"`
	Meta             json.RawMessage          `json:"meta" gorm:"column:meta;type:jsonb"`
	OwnerUser        AccountPublic            `json:"ownerUser" gorm:"foreignKey:owner"`

	DepartmentId      uuid.NullUUID          `json:"departmentId" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
	NavigationPath    string                 `json:"navigationPath" gorm:"navigation_path"`
	TreeIndex         int                    `json:"treeIndex" gorm:"tree_index" `
	Level             int                    `json:"level" gorm:"level" `
	InlineChildren    bool                   `json:"inlineChildren" gorm:"inline_children"`
	Children          []ContentForHandlebars `json:"children" gorm:"-"`
	IsPrimary         bool                   `json:"isPrimary" gorm:"<-:false;column:is_primary;"`
	IsNavigationGroup bool                   `json:"isNavigationGroup" gorm:"<-:false;column:is_navigation_group;"`
	EditLink          string
	PublishAt         *time.Time `json:"publish_at" gorm:"column:publish_at;type:timestamp with time zone"`
	ExpireAt          *time.Time `json:"expire_at" gorm:"column:expire_at;type:timestamp with time zone"`
}

func (c *ContentForHandlebars) AfterFind(tx *gorm.DB) error {
	if c.Type == commonModels.CSS || c.Type == commonModels.JS || c.Type == commonModels.ExternalLinkContentType ||
		c.Type == commonModels.DistributedPage || c.Type == commonModels.Template {
		return nil
	}

	r, ok := tx.Statement.Context.Value("app_context").(*shared.AppContext)
	if !ok {
		c.SiteStructs = []tenancyModels.Site{}
		return nil
	}

	c.SiteStructs = r.SiteStructs(c.Sites)

	return nil
}

func MapTagsForContent(db *gorm.DB, tagIds []uuid.UUID) ([]commonModels.Tag, error) {
	var tags = []commonModels.Tag{}
	return tags, db.
		Where(pgxx.ArrayHasAny("types", []string{"page", "news", "event", "alert", "fragment"})).
		Where("active").
		Where(pgxx.FieldInArray("id", tagIds)).
		Find(&tags).Error
}

func MapTagsForContents(db *gorm.DB, content []ContentForHandlebars) ([]ContentForHandlebars, error) {
	// Cache tags per tenant?
	var tags = []commonModels.Tag{}
	if err := db.
		Where(pgxx.ArrayHasAny("types", []string{"page", "news", "event", "alert", "fragment"})).
		Where("active").
		Find(&tags).Error; err != nil {
		return content, err
	}
	m := slicexx.AsMap(tags, func(t commonModels.Tag) uuid.UUID {
		return t.ID
	})
	for i, c := range content {
		var contentTags = []commonModels.Tag{}
		for _, tagId := range c.TagIds {
			if tag, ok := m[tagId]; ok {
				contentTags = append(contentTags, tag)
			}
		}
		content[i].Tags = contentTags
	}
	return content, nil
}

func BuildBreadcrumbs(nodes []ContentForHandlebars, id uuid.UUID) []ContentForHandlebars {
	for _, n := range nodes {
		if n.ID == id {
			return []ContentForHandlebars{n}
		}
		if len(n.Children) > 0 {
			if path := BuildBreadcrumbs(n.Children, id); len(path) > 0 {
				return append([]ContentForHandlebars{n}, path...)
			}
		}
	}
	return []ContentForHandlebars{}
}

type ContentForHandlebarsChain []ContentForHandlebars

func (c ContentForHandlebars) String() string {
	return c.Title
}
func (c ContentForHandlebars) IsValid() bool {
	return c.ID != uuid.Nil
}

func (c ContentForHandlebars) IsPublished(now time.Time) bool {
	if c.PublishAt == nil {
		return false
	}

	return c.PublishAt.Before(now) && (c.ExpireAt == nil || c.ExpireAt.After(now))
}

func (c ContentForHandlebars) GenerateEditLink(adminURLForTenant string, siteID uuid.UUID) string {
	return fmt.Sprintf("https://%s/content-editor/%s?siteId=%s", adminURLForTenant, c.ID, siteID)
}

func (c ContentForHandlebarsChain) Splice(startIndex, deleteCount int, insert ...ContentForHandlebars) ContentForHandlebarsChain {
	if len(insert) == 0 {
		return c
	}
	endIndex := startIndex + deleteCount
	maxIndex := len(c) - 1
	if maxIndex < startIndex {
		startIndex = maxIndex
	}
	if len(c) < endIndex {
		endIndex = maxIndex
	}
	before := c[:startIndex]
	after := c[endIndex:]
	b := append(before, insert...)
	return append(b, after...)
}

func (ContentForHandlebars) TableName() string {
	return "content"
}

func (c ContentForHandlebars) GetKey() string {
	return c.ID.String()
}

var _ shared.IHaveKey = (*ContentForHandlebars)(nil)

func FromContent(content commonModels.Content) ContentForHandlebars {
	return ContentForHandlebars{
		ID:           content.ID,
		Type:         content.Type,
		Owner:        content.Owner,
		Publisher:    content.Publisher,
		Title:        content.Title,
		Content:      content.Content,
		Data:         content.Data,
		Structure:    content.Structure,
		Route:        content.Route,
		Path:         content.Path,
		PageLayout:   content.PageLayout,
		Created:      content.Created,
		Updated:      content.Updated,
		Deleted:      content.Deleted,
		PrivacyLevel: content.PrivacyLevel,
		Approved:     content.Approved,
		Active:       content.Active,
		Sites:        content.Sites,
		MediaID:      content.MediaID,
		Settings:     content.Settings,
		Status:       content.Status,
		Tags:         content.Tags,
		Media:        content.Media,
		Meta:         content.Meta,
		PublishAt:    content.PublishAt,
		ExpireAt:     content.ExpireAt,
	}
}

type EventInfo struct {
	StartDate      time.Time  `json:"startdate"`
	EndDate        time.Time  `json:"enddate"`
	IsAllDay       bool       `json:"isAllDay"`
	ExpirationDate *time.Time `json:"expirationDate"`
	RRule          string     `json:"rrule"`
	Location       Location   `json:"location"`
}
type Location struct {
	DisplayName string `json:"displayName"`
}

func (c ContentForHandlebars) GetEventInfo() (EventInfo, error) {
	var event EventInfo
	return event, json.Unmarshal(c.Settings, &event)
}
